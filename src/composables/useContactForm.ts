import { ref } from 'vue'
import type { FormData } from '@/types'
import { validateForm, resetForm } from '@/utils/form'

export const useContactForm = () => {
  const form = ref<FormData>(resetForm())
  const isSubmitting = ref(false)
  const submitMessage = ref('')

  const handleSubmit = async () => {
    const validation = validateForm(form.value)
    
    if (!validation.isValid) {
      alert(validation.errors.join('\n'))
      return
    }

    isSubmitting.value = true
    
    try {
      // 这里可以添加实际的API调用
      console.log('表单已提交:', form.value)
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      submitMessage.value = '表单已提交成功！我们将在24小时内与您联系。'
      form.value = resetForm()
      
      // 3秒后清除消息
      setTimeout(() => {
        submitMessage.value = ''
      }, 3000)
      
    } catch (error) {
      console.error('提交失败:', error)
      submitMessage.value = '提交失败，请稍后重试。'
    } finally {
      isSubmitting.value = false
    }
  }

  return {
    form,
    isSubmitting,
    submitMessage,
    handleSubmit
  }
}
