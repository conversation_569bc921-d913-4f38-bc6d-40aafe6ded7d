import { createRouter, createWebHistory } from 'vue-router'
import Home from '@/views/Home.vue'
import EduPlatform from '@/views/products/EduPlatform.vue'
import Company from '@/views/about/Company.vue'
import Careers from '@/views/about/Careers.vue'
import StudyAbroad from '@/views/solutions/StudyAbroad.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  scrollBehavior() {
    // 始终滚动到顶部
    return { top: 0 }
  },
  routes: [
    {
      path: '/',
      name: 'home',
      component: Home
    },
    {
      path: '/products/edu-platform',
      name: 'eduPlatform',
      component: EduPlatform
    },
    {
      path: '/solutions/study-abroad',
      name: 'studyAbroad',
      component: StudyAbroad
    },
    {
      path: '/about/company',
      name: 'company',
      component: Company
    },
    {
      path: '/about/careers',
      name: 'careers',
      component: Careers
    }
  ]
})

export default router