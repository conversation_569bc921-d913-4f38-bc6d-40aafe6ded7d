import type { FormData } from '@/types'

/**
 * 验证邮箱格式
 */
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * 验证手机号格式
 */
export const validatePhone = (phone: string): boolean => {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

/**
 * 验证表单数据
 */
export const validateForm = (form: FormData): { isValid: boolean; errors: string[] } => {
  const errors: string[] = []

  if (!form.name.trim()) {
    errors.push('请输入姓名')
  }

  if (!form.phone.trim()) {
    errors.push('请输入联系电话')
  } else if (!validatePhone(form.phone)) {
    errors.push('请输入正确的手机号码')
  }

  if (!form.email.trim()) {
    errors.push('请输入邮箱地址')
  } else if (!validateEmail(form.email)) {
    errors.push('请输入正确的邮箱地址')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * 重置表单数据
 */
export const resetForm = (): FormData => ({
  name: '',
  company: '',
  phone: '',
  email: '',
  message: ''
})
