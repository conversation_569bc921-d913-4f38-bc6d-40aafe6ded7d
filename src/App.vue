<template>
  <div class="min-h-screen bg-gray-50 flex flex-col">
    <nav class="glass fixed w-full top-0 z-50 border-b border-white/20 backdrop-blur-lg">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-20">
          <div class="flex-shrink-0 flex items-center">
            <router-link to="/" class="block group">
              <img class="h-8 w-auto transition-transform duration-300 group-hover:scale-105" src="/logo/logo.svg" alt="囤鼠科技" />
            </router-link>
          </div>
          <div class="hidden sm:flex sm:items-center sm:space-x-2">
            <!-- 首页 -->
            <router-link to="/" class="nav-item" exact-active-class="active">
              首页
            </router-link>

            <!-- 产品下拉菜单 -->
            <div class="relative group">
              <div class="nav-item cursor-default" :class="{ 'active': $route.path.startsWith('/products') }">
                囤鼠产品
              </div>
              <div class="dropdown-menu">
                <router-link to="/products/edu-platform" class="dropdown-item" active-class="active-dropdown">
                  TunShuEdu AI智教平台
                </router-link>
              </div>
            </div>

            <!-- 解决方案下拉菜单 -->
            <div class="relative group">
              <div class="nav-item cursor-default" :class="{ 'active': $route.path.startsWith('/solutions') }">
                解决方案
              </div>
              <div class="dropdown-menu">
                <router-link to="/solutions/study-abroad" class="dropdown-item" active-class="active-dropdown">
                  留学咨询解决方案
                </router-link>
              </div>
            </div>

            <!-- 关于我们下拉菜单 -->
            <div class="relative group">
              <div class="nav-item cursor-default" :class="{ 'active': $route.path.startsWith('/about') }">
                关于我们
              </div>
              <div class="dropdown-menu">
                <router-link to="/about/company" class="dropdown-item" active-class="active-dropdown">
                  关于我们
                </router-link>
                <router-link to="/about/careers" class="dropdown-item" active-class="active-dropdown">
                  加入我们
                </router-link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <main class="flex-grow pt-20">
      <router-view />
    </main>

    <Footer />
  </div>
</template>

<script setup lang="ts">
import Footer from './components/Footer.vue'
</script>

<style>
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

.nav-item {
  @apply px-4 py-2 text-gray-700 font-medium text-base inline-flex items-center transition-all duration-300 rounded-lg hover:bg-white/50;
  height: 80px;
  line-height: 80px;
  position: relative;
}

.nav-item:hover,
.group:hover .nav-item {
  @apply text-primary-600 transform scale-105;
}

.nav-item.active {
  @apply text-primary-600 bg-white/30;
}

.nav-item.active::after {
  content: '';
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  @apply bg-primary-600;
}

.dropdown-menu {
  @apply absolute left-1/2 mt-0 w-64 bg-white/95 backdrop-blur-md shadow-2xl rounded-xl py-3 opacity-0 invisible border border-white/20;
  transform: translateX(-50%) translateY(-10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 50;
}

.group:hover .dropdown-menu {
  @apply opacity-100 visible;
  transform: translateX(-50%) translateY(0);
}

.dropdown-item {
  @apply block px-6 py-3 text-base text-gray-700 hover:bg-primary-50 hover:text-primary-600 transition-all duration-200 rounded-lg mx-2;
  white-space: nowrap;
}

.dropdown-item:hover {
  @apply transform translate-x-1;
}

.active-dropdown {
  @apply text-primary-600 bg-primary-50;
}

.dropdown-divider {
  @apply my-2 border-t border-gray-100 mx-4;
}

.dropdown-email {
  @apply px-6 py-3;
}
</style>