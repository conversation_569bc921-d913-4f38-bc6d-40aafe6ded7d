<!-- 页面头部组件 - 用于展示页面标题和副标题 -->
<template>
  <div class="relative bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 py-32 overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden">
      <!-- 渐变背景 -->
      <div class="absolute inset-0 bg-gradient-to-br from-primary-500/20 via-purple-600/20 to-blue-600/20"></div>

      <!-- 网格图案 -->
      <div class="absolute inset-0 bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] [background-size:20px_20px] opacity-10"></div>

      <!-- 静态几何图形 -->
      <div class="absolute top-10 right-10 w-32 h-32 bg-primary-500/20 rounded-full blur-xl"></div>
      <div class="absolute bottom-10 left-10 w-48 h-48 bg-purple-500/20 rounded-full blur-xl"></div>
      <div class="absolute top-1/2 left-1/3 w-24 h-24 bg-blue-500/20 rounded-full blur-lg"></div>

      <!-- 光效 -->
      <div class="absolute inset-0 bg-gradient-to-t from-transparent via-transparent to-white/5"></div>
    </div>

    <div class="relative mx-auto max-w-7xl px-6 lg:px-8 text-center">
      <h1 class="text-5xl lg:text-7xl font-bold tracking-tight text-white opacity-0 fade-in-up">
        <span class="block">
          <slot></slot>
        </span>
      </h1>
      <p v-if="$slots.subtitle" class="mt-8 text-xl lg:text-2xl leading-relaxed text-gray-300 opacity-0 fade-in-up delay-200 max-w-4xl mx-auto">
        <slot name="subtitle"></slot>
      </p>

      <!-- 装饰性分隔线 -->
      <div class="mt-12 flex justify-center opacity-0 fade-in-up delay-400">
        <div class="w-24 h-1 bg-gradient-to-r from-primary-500 to-purple-500 rounded-full"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// No props or additional logic needed
</script>

<style scoped>
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fade-in-up 0.8s ease-out forwards;
}
</style>