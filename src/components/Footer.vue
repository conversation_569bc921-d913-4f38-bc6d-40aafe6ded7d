<template>
  <footer class="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-gray-300 relative overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden">
      <div class="absolute top-0 left-0 w-96 h-96 bg-primary-500/5 rounded-full blur-3xl"></div>
      <div class="absolute bottom-0 right-0 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl"></div>
    </div>

    <div class="relative mx-auto max-w-7xl px-6 py-16 lg:px-8 lg:py-20">
      <!-- 主要内容区域 -->
      <div class="grid grid-cols-1 lg:grid-cols-5 gap-12 mb-16">
        <!-- 公司信息 -->
        <div class="lg:col-span-2 space-y-6">
          <div class="flex items-center space-x-3">
            <img class="h-8 w-auto" src="/logo/logo.svg" alt="囤鼠科技" />
          </div>
          <p class="text-gray-400 leading-relaxed max-w-md">
            致力于吸收前沿AI科技，深入行业需求场景，搭建核心技术与行业应用的桥梁，提供领先的智能化解决方案。
          </p>
        </div>

        <!-- 导航链接 -->
        <div class="lg:col-span-3 grid grid-cols-1 md:grid-cols-3 gap-8">
          <!-- 囤鼠产品 -->
          <div class="space-y-6">
            <h3 class="text-lg font-semibold text-white flex items-center">
              <div class="w-1 h-6 bg-primary-500 rounded-full mr-3"></div>
              囤鼠产品
            </h3>
            <ul role="list" class="space-y-3">
              <li>
                <router-link to="/products/edu-platform"
                           class="text-gray-400 hover:text-primary-400 transition-colors duration-300 flex items-center group"
                           @click="scrollToTop">
                  <svg class="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                  TunShuEdu AI智教平台
                </router-link>
              </li>
            </ul>
          </div>

          <!-- 解决方案 -->
          <div class="space-y-6">
            <h3 class="text-lg font-semibold text-white flex items-center">
              <div class="w-1 h-6 bg-primary-500 rounded-full mr-3"></div>
              解决方案
            </h3>
            <ul role="list" class="space-y-3">
              <li>
                <router-link to="/solutions/study-abroad"
                           class="text-gray-400 hover:text-primary-400 transition-colors duration-300 flex items-center group"
                           @click="scrollToTop">
                  <svg class="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                  智能留学教育解决方案
                </router-link>
              </li>
            </ul>
          </div>

          <!-- 关于我们 -->
          <div class="space-y-6">
            <h3 class="text-lg font-semibold text-white flex items-center">
              <div class="w-1 h-6 bg-primary-500 rounded-full mr-3"></div>
              关于我们
            </h3>
            <ul role="list" class="space-y-3">
              <li>
                <router-link to="/about/company"
                           class="text-gray-400 hover:text-primary-400 transition-colors duration-300 flex items-center group"
                           @click="scrollToTop">
                  <svg class="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                  关于我们
                </router-link>
              </li>
              <li>
                <router-link to="/about/careers"
                           class="text-gray-400 hover:text-primary-400 transition-colors duration-300 flex items-center group"
                           @click="scrollToTop">
                  <svg class="w-4 h-4 mr-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                  加入我们
                </router-link>
              </li>
            </ul>
          </div>
        </div>
      </div>



      <!-- 底部版权信息 -->
      <div class="border-t border-gray-700/50 pt-8">
        <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <p class="text-sm text-gray-400">
            &copy; 2025 囤鼠科技有限公司 版权所有
          </p>
          <div class="flex space-x-6 text-sm">
            <a href="http://www.beian.gov.cn/portal/registerSystemInfo"
               target="_blank"
               rel="noreferrer"
               class="text-gray-400 hover:text-primary-400 transition-colors duration-300">
              川公网安备51019002007817号
            </a>
            <a href="https://beian.miit.gov.cn/"
               target="_blank"
               class="text-gray-400 hover:text-primary-400 transition-colors duration-300">
              蜀ICP备2025128320号
            </a>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'instant'
  })
}
</script>