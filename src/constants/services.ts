import {
  CpuChipIcon,
  ChatBubbleLeftRightIcon,
  EyeIcon,
  UserIcon,
  ChartBarIcon,
  CogIcon
} from '@heroicons/vue/24/outline'
import type { CoreService } from '@/types'

export const CORE_SERVICES: CoreService[] = [
  {
    name: '机器学习模型开发',
    description: '基于深度学习框架，构建高精度预测模型，支持监督学习、无监督学习和强化学习等多种算法',
    icon: CpuChipIcon,
    technologies: ['TensorFlow', 'PyTorch', '深度学习', '模型优化']
  },
  {
    name: '自然语言处理',
    description: '提供文本分析、语义理解、智能对话等NLP服务，支持多语言处理和领域定制化',
    icon: ChatBubbleLeftRightIcon,
    technologies: ['BERT', 'GPT', '语义分析', '智能对话']
  },
  {
    name: '计算机视觉应用',
    description: '图像识别、目标检测、人脸识别等视觉AI技术，广泛应用于安防、医疗、制造等领域',
    icon: EyeIcon,
    technologies: ['OpenCV', 'YOLO', '图像识别', '目标检测']
  },
  {
    name: 'AI智能体定制',
    description: '构建智能化AI Agent，实现自主决策和任务执行，提升业务流程自动化水平',
    icon: UserIcon,
    technologies: ['Agent框架', '决策算法', '任务规划', '多模态交互']
  },
  {
    name: '数据分析与预测',
    description: '运用先进的数据挖掘和预测算法，为企业提供精准的业务洞察和趋势预测',
    icon: ChartBarIcon,
    technologies: ['数据挖掘', '预测分析', '商业智能', '可视化']
  },
  {
    name: 'AI系统集成',
    description: '提供端到端的AI解决方案，包括系统架构设计、模型部署、性能优化和运维支持',
    icon: CogIcon,
    technologies: ['系统架构', '模型部署', 'MLOps', '云原生']
  }
]
