import {
  CodeBracketIcon,
  ChartBarIcon,
  AcademicCapIcon,
  HeartIcon,
  RocketLaunchIcon,
  UserGroupIcon,
  LightBulbIcon,
  GlobeAltIcon
} from '@heroicons/vue/24/outline'

// 职位信息类型定义
export interface Position {
  title: string
  description: string
  icon: any
  location?: string
  type?: string
  requirements: string[]
}

// 福利信息类型定义
export interface Benefit {
  name: string
  description: string
  icon: any
  details?: string[]
}

// 开放职位
export const POSITIONS: Position[] = [
  {
    title: '大模型算法工程师',
    description: '负责AI大模型及相关应用的研发工作，推动产品技术创新，参与前沿AI技术的探索与实践。',
    icon: CodeBracketIcon,
    location: '成都',
    type: '全职',
    requirements: [
      '计算机相关专业或STEM专业本科及以上学历',
      '对全栈开发工作有兴趣，具备良好的编程基础',
      '熟悉主流大模型框架和应用范式（如Transformer、GPT等）',
      '有相关项目或工作经验优先，对AI技术充满热情',
      '具备良好的团队协作能力和学习能力'
    ]
  },
  {
    title: '销售工程师',
    description: '负责AI产品的推广与销售，助力商业化模式展开，与客户建立长期合作关系。',
    icon: ChartBarIcon,
    location: '成都',
    type: '全职',
    requirements: [
      '本科及以上学历，市场营销、商务或相关专业优先',
      '对前沿科技尤其是大模型应用产品有学习兴趣',
      '能独立完成客户拓展和产品交付，提供必要的需求支持',
      '有科技领域销售工作经验优先，具备优秀的沟通能力',
      '具备敏锐的市场洞察力和客户服务意识'
    ]
  }
]

// 企业福利与文化
export const BENEFITS: Benefit[] = [
  {
    name: '技术成长',
    description: '在AI前沿领域深度学习，与行业专家共同成长，掌握最新技术趋势。',
    icon: AcademicCapIcon,
    details: [
      '内部技术分享会',
      '外部培训机会',
      '技术会议参与',
      '开源项目贡献'
    ]
  },
  {
    name: '优质薪酬',
    description: '具有竞争力的薪资待遇，完善的福利保障，让您专注于创新工作。',
    icon: HeartIcon,
    details: [
      '竞争力薪资',
      '完善五险一金',
      '年终奖金',
      '节假日福利',
      '定期体检'
    ]
  },
  {
    name: '发展机遇',
    description: '扁平化管理模式，开放的晋升通道，让每个人都有施展才华的机会。',
    icon: RocketLaunchIcon,
    details: [
      '快速晋升通道',
      '跨部门轮岗',
      '项目负责机会',
      '创新项目孵化'
    ]
  },
  {
    name: '团队文化',
    description: '开放包容的团队氛围，鼓励创新思维，重视每个人的想法和贡献。',
    icon: UserGroupIcon,
    details: [
      '开放沟通环境',
      '团队建设活动',
      '创新想法鼓励',
      '多元化团队'
    ]
  },
  {
    name: '创新环境',
    description: '前沿的技术设备，舒适的办公环境，激发创造力的工作空间。',
    icon: LightBulbIcon,
    details: [
      '最新技术设备',
      '舒适办公环境',
      '创意工作空间',
      '灵活工作时间'
    ]
  },
  {
    name: '工作平衡',
    description: '合理的工作安排和充足的休息时间，帮助员工实现工作与生活的平衡。',
    icon: GlobeAltIcon,
    details: [
      '标准工作时间',
      '充足休息时间',
      '带薪年假',
      '健康生活倡导'
    ]
  }
]

// 招聘联系信息
export const RECRUITMENT_CONTACT = {
  email: '<EMAIL>',
  platforms: [
    {
      name: 'Boss直聘',
      searchTerm: '囤鼠科技'
    },
    {
      name: '实习僧',
      searchTerm: '囤鼠科技'
    }
  ]
}
