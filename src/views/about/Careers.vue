<!-- 招聘页面 -->
<template>
  <div class="min-h-screen bg-gradient-to-br from-gray-50 to-white">
    <PageHeader>
      加入我们
      <template #subtitle>
        探索AI的无限可能，共同推动产业数字化转型
      </template>
    </PageHeader>

    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <div class="absolute top-20 left-10 w-32 h-32 bg-primary-500/10 rounded-full blur-xl"></div>
      <div class="absolute bottom-20 right-10 w-48 h-48 bg-blue-500/10 rounded-full blur-xl"></div>
      <div class="absolute top-1/2 left-1/4 w-24 h-24 bg-purple-500/10 rounded-full blur-lg"></div>
    </div>

    <div class="relative mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8">
      <!-- 引人入胜的开篇 -->
      <div class="mx-auto max-w-4xl text-center opacity-0 fade-in-up relative">
        <!-- 背景装饰 -->
        <div class="absolute inset-0 bg-gradient-to-r from-primary-50 via-blue-50 to-purple-50 rounded-3xl opacity-50 blur-3xl"></div>

        <div class="relative bg-white rounded-3xl p-8 lg:p-12 shadow-xl border border-gray-100 overflow-hidden">
          <!-- 卡片内装饰 -->
          <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-primary-100 to-blue-100 rounded-full opacity-50 transform translate-x-16 -translate-y-16"></div>
          <div class="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-purple-100 to-pink-100 rounded-full opacity-50 transform -translate-x-12 translate-y-12"></div>

          <div class="relative">
            <h2 class="text-4xl lg:text-5xl font-bold tracking-tight text-gray-900 mb-6">
              开启您的
              <span class="text-gradient bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent">AI职业之旅</span>
            </h2>
            <p class="text-xl leading-relaxed text-gray-700 mb-8 max-w-3xl mx-auto">
              在囤鼠科技，您将站在AI技术的最前沿，与顶尖的技术团队一起探索人工智能的无限可能。我们不仅仅是在开发产品，更是在塑造未来，推动整个产业的数字化转型。
            </p>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
              <div class="p-4 bg-gradient-to-br from-primary-50 to-primary-100 rounded-xl">
                <div class="text-2xl font-bold text-primary-600 mb-2">前沿技术</div>
                <div class="text-sm text-gray-600">深度参与AI大模型研发</div>
              </div>
              <div class="p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl">
                <div class="text-2xl font-bold text-blue-600 mb-2">创新文化</div>
                <div class="text-sm text-gray-600">扁平化管理，鼓励创新思维</div>
              </div>
              <div class="p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl">
                <div class="text-2xl font-bold text-purple-600 mb-2">成长空间</div>
                <div class="text-sm text-gray-600">与行业专家共同成长</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 职位列表 -->
      <div class="mx-auto mt-24 max-w-2xl sm:mt-32 lg:max-w-none">
        <div class="text-center mb-16 opacity-0 fade-in-up delay-200">
          <h3 class="text-3xl lg:text-4xl font-bold tracking-tight text-gray-900 mb-6">
            开放
            <span class="text-gradient bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent">职位</span>
          </h3>
          <p class="text-lg text-gray-600 max-w-2xl mx-auto">
            我们正在寻找充满激情的人才，一起在AI领域创造无限可能
          </p>
        </div>

        <div class="grid grid-cols-1 gap-x-8 gap-y-12 lg:grid-cols-2">
          <div v-for="(position, index) in positions" :key="position.title"
               class="bg-white rounded-2xl p-8 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover-float opacity-0 fade-in-up group relative overflow-hidden"
               :class="`delay-${300 + index * 100}`">

            <!-- 卡片装饰 -->
            <div class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-primary-50 to-blue-50 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform translate-x-10 -translate-y-10"></div>

            <div class="relative">
              <!-- 职位标题和图标 -->
              <div class="flex items-center mb-6">
                <div class="relative">
                  <div class="absolute inset-0 bg-primary-500/20 rounded-full blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                  <div class="relative flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-br from-primary-500 to-blue-600 group-hover:from-primary-600 group-hover:to-blue-700 transition-all duration-300 shadow-lg">
                    <component :is="position.icon" class="h-7 w-7 text-white" aria-hidden="true" />
                  </div>
                </div>
                <div class="ml-4 flex-1">
                  <h3 class="text-2xl font-bold text-gray-900 group-hover:text-primary-600 transition-colors duration-300">
                    {{ position.title }}
                  </h3>
                  <div class="mt-1 flex items-center text-sm text-gray-500">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    {{ position.location || '北京/远程' }}
                  </div>
                </div>
              </div>

              <!-- 职位描述 -->
              <p class="text-gray-700 leading-relaxed mb-6 text-base">
                {{ position.description }}
              </p>

              <!-- 职位要求 -->
              <div class="mb-8">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">职位要求</h4>
                <ul class="space-y-3">
                  <li v-for="requirement in position.requirements" :key="requirement" class="flex gap-x-3">
                    <div class="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                      </svg>
                    </div>
                    <span class="text-sm text-gray-600 leading-relaxed">{{ requirement }}</span>
                  </li>
                </ul>
              </div>

              <!-- 立即申请按钮 -->
              <div class="flex items-center justify-between pt-4 border-t border-gray-100">
                <div class="text-sm text-gray-500">
                  <span class="font-medium">薪资面议</span> •
                  <span>{{ position.type || '全职' }}</span>
                </div>
                <a :href="`mailto:<EMAIL>?subject=申请${position.title}-[请填写您的姓名]&body=尊敬的HR，%0A%0A我对${position.title}职位非常感兴趣，现投递简历申请该职位。%0A%0A期待您的回复。%0A%0A此致%0A敬礼`"
                   class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-primary-600 to-blue-600 text-white font-medium rounded-xl hover:from-primary-700 hover:to-blue-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl group">
                  立即申请
                  <svg class="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- 申请流程指引 -->
        <div class="mt-24 opacity-0 fade-in-up delay-500">
          <div class="bg-gradient-to-br from-primary-50 via-blue-50 to-purple-50 rounded-3xl p-8 lg:p-12 relative overflow-hidden">
            <!-- 背景装饰 -->
            <div class="absolute top-0 right-0 w-32 h-32 bg-white/50 rounded-full blur-xl transform translate-x-16 -translate-y-16"></div>
            <div class="absolute bottom-0 left-0 w-24 h-24 bg-white/50 rounded-full blur-lg transform -translate-x-12 translate-y-12"></div>

            <div class="relative text-center">
              <h3 class="text-3xl font-bold text-gray-900 mb-6">
                简历
                <span class="text-gradient bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent">投递指南</span>
              </h3>

              <div class="max-w-4xl mx-auto">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                  <!-- 有明确职位 -->
                  <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
                    <div class="w-12 h-12 bg-gradient-to-br from-primary-100 to-primary-200 rounded-xl flex items-center justify-center mx-auto mb-4">
                      <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                      </svg>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-3">申请特定职位</h4>
                    <p class="text-gray-600 text-sm leading-relaxed mb-4">
                      请将您的简历和求职信发送至邮箱，邮件主题请注明：
                    </p>
                    <div class="bg-gray-50 rounded-lg p-3 text-sm font-mono text-gray-700">
                      申请[职位名称]-[您的姓名]
                    </div>
                  </div>

                  <!-- 人才库 -->
                  <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl flex items-center justify-center mx-auto mb-4">
                      <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                      </svg>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-3">加入人才库</h4>
                    <p class="text-gray-600 text-sm leading-relaxed mb-4">
                      暂无合适职位？欢迎投递简历至人才库，邮件主题请注明：
                    </p>
                    <div class="bg-gray-50 rounded-lg p-3 text-sm font-mono text-gray-700">
                      人才库-[您的姓名]-[专业方向]
                    </div>
                  </div>
                </div>

                <!-- 联系邮箱 -->
                <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 max-w-md mx-auto">
                  <div class="flex items-center justify-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-primary-100 to-blue-100 rounded-lg flex items-center justify-center">
                      <svg class="h-5 w-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                      </svg>
                    </div>
                    <div>
                      <div class="text-sm text-gray-500 mb-1">招聘邮箱</div>
                      <a href="mailto:<EMAIL>" class="text-primary-600 hover:text-primary-700 font-semibold text-lg transition-colors duration-300">
                        <EMAIL>
                      </a>
                    </div>
                  </div>
                </div>

                <p class="text-gray-600 text-sm mt-6 max-w-2xl mx-auto">
                  我们承诺在收到您的简历后 <span class="font-semibold text-gray-900">3个工作日内</span> 给予回复。
                  未来有合适机会时，我们将第一时间与您联系。
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 为什么选择我们 -->
      <div class="mx-auto mt-24 max-w-7xl sm:mt-32">
        <div class="text-center mb-16 opacity-0 fade-in-up delay-600">
          <h3 class="text-3xl lg:text-4xl font-bold tracking-tight text-gray-900 mb-6">
            为什么选择
            <span class="text-gradient bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent">囤鼠科技</span>
          </h3>
          <p class="text-lg text-gray-600 max-w-3xl mx-auto">
            在这里，您不仅仅是员工，更是创新的伙伴。我们致力于打造一个充满活力、鼓励创新、关注成长的工作环境
          </p>
        </div>

        <!-- 企业文化与福利网格 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div v-for="(benefit, index) in benefits" :key="benefit.name"
               class="bg-white rounded-2xl p-8 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover-float opacity-0 fade-in-up group relative overflow-hidden"
               :class="`delay-${700 + index * 100}`">

            <!-- 卡片装饰 -->
            <div class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-primary-50 to-blue-50 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform translate-x-10 -translate-y-10"></div>

            <div class="relative text-center">
              <div class="mb-6">
                <div class="relative inline-block">
                  <div class="absolute inset-0 bg-primary-500/20 rounded-full blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                  <div class="relative flex h-16 w-16 items-center justify-center rounded-xl bg-gradient-to-br from-primary-500 to-blue-600 group-hover:from-primary-600 group-hover:to-blue-700 transition-all duration-300 shadow-lg mx-auto">
                    <component :is="benefit.icon" class="h-8 w-8 text-white group-hover:scale-110 transition-transform duration-300" aria-hidden="true" />
                  </div>
                </div>
              </div>

              <h4 class="text-xl font-bold text-gray-900 group-hover:text-primary-600 transition-colors duration-300 mb-4">
                {{ benefit.name }}
              </h4>

              <p class="text-gray-600 leading-relaxed mb-4">
                {{ benefit.description }}
              </p>

              <!-- 具体福利点 -->
              <ul v-if="benefit.details" class="text-sm text-gray-500 space-y-2">
                <li v-for="detail in benefit.details" :key="detail" class="flex items-center justify-center">
                  <svg class="w-4 h-4 text-green-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  {{ detail }}
                </li>
              </ul>

              <!-- 悬浮时显示的装饰 -->
              <div class="mt-6 flex justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div class="w-12 h-1 bg-gradient-to-r from-primary-500 to-blue-500 rounded-full"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 公司文化亮点 -->
        <div class="mt-16 bg-gradient-to-br from-gray-900 via-primary-900 to-blue-900 rounded-3xl p-8 lg:p-12 text-center opacity-0 fade-in-up delay-1000 relative overflow-hidden">
          <!-- 背景装饰 -->
          <div class="absolute inset-0 overflow-hidden">
            <div class="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full blur-xl transform translate-x-16 -translate-y-16"></div>
            <div class="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full blur-lg transform -translate-x-12 translate-y-12"></div>
          </div>

          <div class="relative">
            <h4 class="text-2xl lg:text-3xl font-bold text-white mb-6">
              加入我们，共创AI未来
            </h4>
            <p class="text-blue-100 text-lg mb-8 max-w-3xl mx-auto leading-relaxed">
              在囤鼠科技，每一天都是新的挑战与机遇。我们相信，最好的产品来自最优秀的团队，
              而最优秀的团队需要最适合的环境来茁壮成长。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
              <div class="p-4 bg-white/10 rounded-xl backdrop-blur-sm">
                <div class="text-2xl font-bold text-white mb-2">扁平化管理</div>
                <div class="text-sm text-blue-100">直接沟通，快速决策</div>
              </div>
              <div class="p-4 bg-white/10 rounded-xl backdrop-blur-sm">
                <div class="text-2xl font-bold text-white mb-2">学习型组织</div>
                <div class="text-sm text-blue-100">持续学习，共同成长</div>
              </div>
              <div class="p-4 bg-white/10 rounded-xl backdrop-blur-sm">
                <div class="text-2xl font-bold text-white mb-2">工作生活平衡</div>
                <div class="text-sm text-blue-100">灵活工作，健康生活</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  CodeBracketIcon,
  ChartBarIcon,
  AcademicCapIcon,
  HeartIcon,
  RocketLaunchIcon,
  UserGroupIcon,
  LightBulbIcon,
  GlobeAltIcon
} from '@heroicons/vue/24/outline'
import PageHeader from '@/components/PageHeader.vue'

const positions = [
  {
    title: '大模型算法工程师',
    description: '负责AI大模型及相关应用的研发工作，推动产品技术创新，参与前沿AI技术的探索与实践。',
    icon: CodeBracketIcon,
    location: '北京/远程',
    type: '全职',
    requirements: [
      '计算机相关专业或STEM专业本科及以上学历',
      '对全栈开发工作有兴趣，具备良好的编程基础',
      '熟悉主流大模型框架和应用范式（如Transformer、GPT等）',
      '有相关项目或工作经验优先，对AI技术充满热情',
      '具备良好的团队协作能力和学习能力'
    ]
  },
  {
    title: '销售工程师',
    description: '负责AI产品的推广与销售，助力商业化模式展开，与客户建立长期合作关系。',
    icon: ChartBarIcon,
    location: '北京/上海',
    type: '全职',
    requirements: [
      '本科及以上学历，市场营销、商务或相关专业优先',
      '对前沿科技尤其是大模型应用产品有学习兴趣',
      '能独立完成客户拓展和产品交付，提供必要的需求支持',
      '有科技领域销售工作经验优先，具备优秀的沟通能力',
      '具备敏锐的市场洞察力和客户服务意识'
    ]
  }
]

const benefits = [
  {
    name: '技术成长',
    description: '在AI前沿领域深度学习，与行业专家共同成长，掌握最新技术趋势。',
    icon: AcademicCapIcon,
    details: [
      '内部技术分享会',
      '外部培训机会',
      '技术会议参与',
      '开源项目贡献'
    ]
  },
  {
    name: '优质薪酬',
    description: '具有竞争力的薪资待遇，完善的福利保障，让您专注于创新工作。',
    icon: HeartIcon,
    details: [
      '竞争力薪资',
      '完善五险一金',
      '年终奖金',
      '节假日福利',
      '定期体检'
    ]
  },
  {
    name: '发展机遇',
    description: '扁平化管理模式，开放的晋升通道，让每个人都有施展才华的机会。',
    icon: RocketLaunchIcon,
    details: [
      '快速晋升通道',
      '跨部门轮岗',
      '项目负责机会',
      '创新项目孵化'
    ]
  },
  {
    name: '团队文化',
    description: '开放包容的团队氛围，鼓励创新思维，重视每个人的想法和贡献。',
    icon: UserGroupIcon,
    details: [
      '开放沟通环境',
      '团队建设活动',
      '创新想法鼓励',
      '多元化团队'
    ]
  },
  {
    name: '创新环境',
    description: '前沿的技术设备，舒适的办公环境，激发创造力的工作空间。',
    icon: LightBulbIcon,
    details: [
      '最新技术设备',
      '舒适办公环境',
      '创意工作空间',
      '灵活工作时间'
    ]
  },
  {
    name: '工作平衡',
    description: '支持远程办公，灵活的工作安排，帮助员工实现工作与生活的平衡。',
    icon: GlobeAltIcon,
    details: [
      '远程办公支持',
      '弹性工作时间',
      '带薪年假',
      '健康生活倡导'
    ]
  }
]
</script>
