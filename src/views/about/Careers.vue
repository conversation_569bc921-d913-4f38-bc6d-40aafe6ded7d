<!-- 招聘页面 -->
<template>
  <div class="min-h-screen bg-gray-50">
    <PageHeader>
      加入我们
      <template #subtitle>
        探索AI的无限可能，共同推动产业数字化转型
      </template>
    </PageHeader>

    <div class="mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8">
      <!-- Introduction -->
      <div class="mx-auto max-w-2xl lg:mx-0 opacity-0 fade-in-up">
        <h2 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">工作机会</h2>
        <p class="mt-6 text-lg leading-8 text-gray-600">
          渴望充满激情的伙伴加入我们的团队，一起描绘AI应用的蓝图。
        </p>
      </div>

      <!-- Positions -->
      <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
        <div class="grid grid-cols-1 gap-x-8 gap-y-16 lg:grid-cols-2">
          <div v-for="(position, index) in positions" :key="position.title" 
               class="card group hover-float p-8 opacity-0 fade-in-up"
               :class="`delay-${(index + 1) * 200}`">
            <div class="flex items-center mb-6">
              <div class="relative">
                <div class="absolute inset-0 bg-primary-500/20 rounded-full blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                <div class="relative flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-primary-500 to-blue-600 group-hover:from-primary-600 group-hover:to-blue-700 transition-all duration-300 shadow-lg">
                  <component :is="position.icon" class="h-6 w-6 text-white" aria-hidden="true" />
                </div>
              </div>
              <h3 class="ml-4 text-xl font-semibold text-gray-900 group-hover:text-primary-600 transition-colors duration-300">{{ position.title }}</h3>
            </div>
            <p class="text-gray-600 leading-relaxed mb-6">{{ position.description }}</p>
            <ul class="space-y-3">
              <li v-for="requirement in position.requirements" :key="requirement" class="flex gap-x-3">
                <div class="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                </div>
                <span class="text-sm text-gray-600">{{ requirement }}</span>
              </li>
            </ul>
          </div>
        </div>

        <!-- Email Application Section -->
        <div class="mt-16 text-center opacity-0 fade-in-up delay-600">
          <div class="card p-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">投递简历</h3>
            <p class="text-gray-600 mb-6">如果您对我们的职位感兴趣，欢迎投递简历</p>
            <div class="inline-flex items-center justify-center space-x-3 bg-primary-50 px-6 py-3 rounded-xl">
              <div class="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
                <svg class="h-5 w-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
              </div>
              <a href="mailto:<EMAIL>" class="text-primary-600 hover:text-primary-700 font-medium text-lg">
                <EMAIL>
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Company Benefits -->
      <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
        <div class="text-center mb-16 opacity-0 fade-in-up delay-800">
          <h3 class="text-3xl font-bold tracking-tight text-gray-900">
            企业
            <span class="text-gradient bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent">福利</span>
          </h3>
          <p class="mt-4 text-lg text-gray-600">为员工提供全方位的保障和发展机会</p>
        </div>
        
        <dl class="grid max-w-xl grid-cols-1 gap-8 lg:max-w-none lg:grid-cols-3">
          <div v-for="(benefit, index) in benefits" :key="benefit.name" 
               class="card group hover-float p-8 text-center opacity-0 fade-in-up"
               :class="`delay-${900 + index * 100}`">
            <dt class="flex flex-col items-center">
              <div class="relative mb-6">
                <div class="absolute inset-0 bg-primary-500/20 rounded-full blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                <div class="relative flex h-16 w-16 items-center justify-center rounded-xl bg-gradient-to-br from-primary-500 to-blue-600 group-hover:from-primary-600 group-hover:to-blue-700 transition-all duration-300 shadow-lg">
                  <component :is="benefit.icon" class="h-8 w-8 text-white group-hover:scale-110 transition-transform duration-300" aria-hidden="true" />
                </div>
              </div>
              <h4 class="text-xl font-semibold text-gray-900 group-hover:text-primary-600 transition-colors duration-300">
                {{ benefit.name }}
              </h4>
            </dt>
            <dd class="mt-4 text-gray-600 leading-relaxed">
              {{ benefit.description }}
            </dd>
            
            <!-- 悬浮时显示的装饰 -->
            <div class="mt-6 flex justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <div class="w-12 h-1 bg-gradient-to-r from-primary-500 to-blue-500 rounded-full"></div>
            </div>
          </div>
        </dl>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  CodeBracketIcon, 
  ChartBarIcon, 
  AcademicCapIcon,
  HeartIcon,
  RocketLaunchIcon
} from '@heroicons/vue/24/outline'
import PageHeader from '@/components/PageHeader.vue'

const positions = [
  {
    title: '大模型算法工程师',
    description: '负责AI大模型及相关应用的研发工作，推动产品技术创新。',
    icon: CodeBracketIcon,
    requirements: [
      '计算机相关专业或STEM专业本科及以上学历',
      '对全栈开发工作有兴趣',
      '熟悉主流大模型框架和应用范式',
      '有相关项目或工作经验优先'
    ]
  },
  {
    title: '销售工程师',
    description: '负责AI产品的推广与销售，助力商业化模式展开。',
    icon: ChartBarIcon,
    requirements: [
      '本科及以上学历',
      '对前沿科技尤其是大模型应用产品有学习兴趣',
      '能独立完成客户拓展和产品交付，提供必要的需求支持',
      '有科技领域销售工作经验优先'
    ]
  }
]

const benefits = [
  {
    name: '技术成长',
    description: '提供丰富的培训资源和学习机会，支持员工职业发展。',
    icon: AcademicCapIcon,
  },
  {
    name: '优质薪酬',
    description: '具有竞争力的薪资待遇，完善的五险一金，节假日礼品及定期体检。',
    icon: HeartIcon,
  },
  {
    name: '发展机遇',
    description: '扁平化管理模式，开放的晋升通道，让每个人都有施展才华的机会。',
    icon: RocketLaunchIcon,
  }
]
</script>
