<!-- 公司介绍页面 -->
<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 页面头部 -->
    <PageHeader>
      关于囤鼠科技
      <template #subtitle>
        洞见AI未来，共塑智能时代 — 了解我们的故事、使命与驱动力
      </template>
    </PageHeader>

    <div class="mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8">
      <!-- 我们是谁 -->
      <div class="mx-auto max-w-4xl opacity-0 fade-in-up">
        <div class="text-center mb-16">
          <h2 class="text-4xl lg:text-5xl font-bold tracking-tight text-gray-900 mb-6">
            {{ companyStory.title }}
          </h2>
          <div class="w-24 h-1 bg-gradient-to-r from-gray-400 to-gray-600 rounded-full mx-auto"></div>
        </div>

        <div class="bg-white rounded-2xl p-8 lg:p-12 shadow-sm border border-gray-100">
          <div class="prose prose-lg prose-gray max-w-none">
            <p class="text-xl leading-relaxed text-gray-700 text-justify">
              {{ companyStory.content }}
            </p>
          </div>

          <!-- 装饰性元素 -->
          <div class="mt-8 flex justify-center">
            <div class="flex space-x-2">
              <div class="w-2 h-2 bg-gray-300 rounded-full"></div>
              <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
              <div class="w-2 h-2 bg-gray-500 rounded-full"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 使命与愿景 -->
      <div class="mx-auto mt-24 max-w-6xl sm:mt-32">
        <div class="text-center mb-16 opacity-0 fade-in-up delay-200">
          <h2 class="text-4xl lg:text-5xl font-bold tracking-tight text-gray-900 mb-6">
            使命与愿景
          </h2>
          <div class="w-24 h-1 bg-gradient-to-r from-gray-400 to-gray-600 rounded-full mx-auto"></div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- 使命 -->
          <div class="opacity-0 fade-in-up delay-300">
            <div class="bg-white rounded-2xl p-8 lg:p-10 shadow-sm border border-gray-100 h-full">
              <div class="flex items-center mb-6">
                <div class="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mr-4">
                  <svg class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                  </svg>
                </div>
                <h3 class="text-2xl font-bold text-gray-900">{{ missionVision.mission.title }}</h3>
              </div>
              <p class="text-lg leading-relaxed text-gray-700">
                {{ missionVision.mission.content }}
              </p>

              <!-- 装饰线条 -->
              <div class="mt-6 w-16 h-1 bg-gradient-to-r from-gray-300 to-gray-500 rounded-full"></div>
            </div>
          </div>

          <!-- 愿景 -->
          <div class="opacity-0 fade-in-up delay-400">
            <div class="bg-white rounded-2xl p-8 lg:p-10 shadow-sm border border-gray-100 h-full">
              <div class="flex items-center mb-6">
                <div class="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mr-4">
                  <svg class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                  </svg>
                </div>
                <h3 class="text-2xl font-bold text-gray-900">{{ missionVision.vision.title }}</h3>
              </div>
              <p class="text-lg leading-relaxed text-gray-700">
                {{ missionVision.vision.content }}
              </p>

              <!-- 装饰线条 -->
              <div class="mt-6 w-16 h-1 bg-gradient-to-r from-gray-300 to-gray-500 rounded-full"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 核心价值观 -->
      <div class="mx-auto mt-24 max-w-6xl sm:mt-32">
        <div class="text-center mb-16 opacity-0 fade-in-up delay-500">
          <h2 class="text-4xl lg:text-5xl font-bold tracking-tight text-gray-900 mb-6">
            核心价值观
          </h2>
          <p class="text-lg text-gray-600 max-w-2xl mx-auto">指导我们前进的核心理念，塑造我们的企业文化和行为准则</p>
          <div class="w-24 h-1 bg-gradient-to-r from-gray-400 to-gray-600 rounded-full mx-auto mt-6"></div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div v-for="(value, index) in coreValues" :key="value.name"
               class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 text-center group hover:shadow-md transition-all duration-300 opacity-0 fade-in-up"
               :class="`delay-${600 + index * 100}`">
            <div class="mb-6">
              <div class="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto group-hover:bg-gray-200 transition-colors duration-300">
                <component :is="value.icon" class="h-8 w-8 text-gray-700 group-hover:text-gray-900 transition-colors duration-300" aria-hidden="true" />
              </div>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-4 group-hover:text-gray-800 transition-colors duration-300">
              {{ value.name }}
            </h3>
            <p class="text-gray-600 leading-relaxed text-sm group-hover:text-gray-700 transition-colors duration-300">
              {{ value.description }}
            </p>

            <!-- 装饰线条 -->
            <div class="mt-6 w-12 h-1 bg-gradient-to-r from-gray-300 to-gray-500 rounded-full mx-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </div>
        </div>
      </div>

      <!-- 发展历程 -->
      <div class="mx-auto mt-24 max-w-5xl sm:mt-32">
        <div class="text-center mb-16 opacity-0 fade-in-up delay-1000">
          <h2 class="text-4xl lg:text-5xl font-bold tracking-tight text-gray-900 mb-6">
            发展历程
          </h2>
          <p class="text-lg text-gray-600 max-w-2xl mx-auto">见证我们的成长足迹，每一个里程碑都标志着我们在AI技术道路上的坚实步伐</p>
          <div class="w-24 h-1 bg-gradient-to-r from-gray-400 to-gray-600 rounded-full mx-auto mt-6"></div>
        </div>

        <div class="relative">
          <!-- 时间线背景 -->
          <div class="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-gray-300 via-gray-400 to-gray-500"></div>

          <div class="space-y-8">
            <div v-for="(milestone, milestoneIdx) in companyMilestones" :key="milestone.year"
                 class="relative opacity-0 fade-in-up"
                 :class="`delay-${1100 + milestoneIdx * 100}`">
              <!-- 时间节点 -->
              <div class="absolute left-6 w-4 h-4 bg-white border-4 border-gray-500 rounded-full shadow-lg"></div>

              <!-- 内容卡片 -->
              <div class="ml-20">
                <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 group hover:shadow-md transition-all duration-300">
                  <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                      <div class="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center group-hover:bg-gray-200 transition-colors duration-300">
                        <component :is="milestone.icon" class="h-6 w-6 text-gray-700 group-hover:text-gray-900 transition-colors duration-300" aria-hidden="true" />
                      </div>
                    </div>
                    <div class="flex-1 min-w-0">
                      <div class="flex items-center space-x-3 mb-3">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                          {{ milestone.year }}
                        </span>
                      </div>
                      <p class="text-gray-700 leading-relaxed group-hover:text-gray-900 transition-colors duration-300">
                        {{ milestone.content }}
                      </p>
                    </div>
                  </div>

                  <!-- 装饰线条 -->
                  <div class="mt-4 w-16 h-1 bg-gradient-to-r from-gray-300 to-gray-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 团队风采 -->
      <div class="mx-auto mt-24 max-w-6xl sm:mt-32">
        <div class="text-center mb-16 opacity-0 fade-in-up delay-1600">
          <h2 class="text-4xl lg:text-5xl font-bold tracking-tight text-gray-900 mb-6">
            {{ teamInfo.title }}
          </h2>
          <p class="text-lg text-gray-600 max-w-3xl mx-auto">{{ teamInfo.description }}</p>
          <div class="w-24 h-1 bg-gradient-to-r from-gray-400 to-gray-600 rounded-full mx-auto mt-6"></div>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          <div v-for="(stat, index) in teamInfo.stats" :key="stat.label"
               class="bg-white rounded-2xl p-6 text-center shadow-sm border border-gray-100 group hover:shadow-md transition-all duration-300 opacity-0 fade-in-up"
               :class="`delay-${1700 + index * 100}`">
            <div class="mb-4">
              <div class="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto group-hover:bg-gray-200 transition-colors duration-300">
                <component :is="stat.icon" class="h-8 w-8 text-gray-700 group-hover:text-gray-900 transition-colors duration-300" aria-hidden="true" />
              </div>
            </div>
            <div class="text-3xl font-bold text-gray-900 mb-2 group-hover:text-gray-800 transition-colors duration-300">{{ stat.value }}</div>
            <div class="text-gray-600 text-sm group-hover:text-gray-700 transition-colors duration-300">{{ stat.label }}</div>

            <!-- 装饰线条 -->
            <div class="mt-4 w-12 h-1 bg-gradient-to-r from-gray-300 to-gray-500 rounded-full mx-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </div>
        </div>
      </div>

      <!-- 行动号召 -->
      <div class="mx-auto mt-24 max-w-4xl sm:mt-32 mb-16">
        <div class="bg-gradient-to-br from-gray-900 to-gray-800 rounded-3xl p-8 lg:p-12 text-center opacity-0 fade-in-up delay-2100">
          <h2 class="text-3xl lg:text-4xl font-bold text-white mb-6">
            {{ callToAction.title }}
          </h2>
          <p class="text-xl text-gray-300 mb-8 max-w-2xl mx-auto leading-relaxed">
            {{ callToAction.description }}
          </p>

          <div class="flex flex-col sm:flex-row items-center justify-center gap-4">
            <template v-for="(button, index) in callToAction.buttons" :key="button.text">
              <router-link v-if="button.link.startsWith('/')" :to="button.link"
                           :class="[
                             'inline-flex items-center px-6 py-3 rounded-xl font-medium transition-all duration-300 group',
                             button.type === 'primary'
                               ? 'bg-white text-gray-900 hover:bg-gray-100 hover:scale-105'
                               : 'bg-transparent text-white border-2 border-white hover:bg-white hover:text-gray-900'
                           ]">
                {{ button.text }}
                <svg class="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                </svg>
              </router-link>
              <a v-else :href="button.link"
                 :class="[
                   'inline-flex items-center px-6 py-3 rounded-xl font-medium transition-all duration-300 group',
                   button.type === 'primary'
                     ? 'bg-white text-gray-900 hover:bg-gray-100 hover:scale-105'
                     : 'bg-transparent text-white border-2 border-white hover:bg-white hover:text-gray-900'
                 ]">
                {{ button.text }}
                <svg class="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                </svg>
              </a>
            </template>
          </div>

          <!-- 装饰性元素 -->
          <div class="mt-8 flex justify-center">
            <div class="flex space-x-2">
              <div class="w-2 h-2 bg-white/30 rounded-full"></div>
              <div class="w-2 h-2 bg-white/50 rounded-full"></div>
              <div class="w-2 h-2 bg-white/70 rounded-full"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  LightBulbIcon,
  HeartIcon,
  RocketLaunchIcon,
  StarIcon,
  BuildingOfficeIcon,
  AcademicCapIcon
} from '@heroicons/vue/24/outline'
import PageHeader from '@/components/PageHeader.vue'

const values = [
  {
    name: '创新驱动',
    description: '持续探索前沿AI技术，以创新思维解决实际问题，推动技术与应用的深度融合。',
    icon: LightBulbIcon
  },
  {
    name: '用户至上',
    description: '始终以用户需求为中心，提供优质的产品和服务，创造真正的价值。',
    icon: HeartIcon
  },
  {
    name: '追求卓越',
    description: '精益求精，不断提升产品质量和服务水平，力求在每个细节上做到最好。',
    icon: StarIcon
  }
]

const milestones = [
  {
    year: '2024',
    content: '囤鼠科技正式成立，专注于AI技术在教育领域的应用与创新。',
    icon: BuildingOfficeIcon
  },
  {
    year: '2024',
    content: 'TunShuEdu AI智教平台正式发布，为教育咨询机构提供智能化解决方案。',
    icon: AcademicCapIcon
  },
  {
    year: '2024',
    content: '与多家知名教育机构建立合作关系，产品在实际应用中获得良好反馈。',
    icon: RocketLaunchIcon
  }
]
</script>
