<!-- TunShuEdu AI智教平台页面 -->
<template>
  <div class="min-h-screen bg-gray-50">
    <PageHeader>
      TunShuEdu AI智教平台
      <template #subtitle>
        基于大模型AI技术，为教育咨询机构提供全方位的智能化服务
      </template>
    </PageHeader>

    <div class="py-24 sm:py-32">
      <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <!-- 产品优势 -->
        <div class="text-center">
          <h2 class="text-3xl font-bold tracking-tight text-primary-600 sm:text-4xl">产品优势</h2>
          <p class="mt-4 text-lg text-gray-600">
            TunShuEdu集成了最先进的大语言模型，具备处理多模态信息的能力，同时本地部署数据库，杜绝信息价值泄漏。
          </p>
        </div>

        <!-- 核心价值功能 -->
        <div class="mt-32">
          <div class="text-center mb-16">
            <h2 class="text-3xl font-bold tracking-tight text-primary-600 sm:text-4xl">核心价值功能</h2>
          </div>

          <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <dl class="grid grid-cols-1 gap-8 lg:grid-cols-3">
              <div v-for="(feature, index) in features" :key="feature.name" 
                   class="card group hover-float p-8 opacity-0 fade-in-up"
                   :class="`delay-${(index + 1) * 100}`">
                <dt class="flex flex-col items-center gap-6">
                  <div class="relative">
                    <!-- 背景光效 -->
                    <div class="absolute inset-0 bg-primary-500/20 rounded-full blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                    <div class="relative flex h-20 w-20 items-center justify-center rounded-xl bg-gradient-to-br from-primary-100 to-primary-200 group-hover:from-primary-200 group-hover:to-primary-300 transition-all duration-300 shadow-lg">
                      <component :is="feature.icon" class="h-10 w-10 text-primary-600 group-hover:scale-110 transition-transform duration-300" aria-hidden="true" />
                    </div>
                  </div>
                  <h3 class="text-xl font-semibold text-gray-900 group-hover:text-primary-600 transition-colors duration-300">{{ feature.name }}</h3>
                </dt>
                <dd class="mt-6 text-center text-gray-600 leading-relaxed">{{ feature.description }}</dd>
                
                <!-- 悬浮时显示的装饰 -->
                <div class="mt-6 flex justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div class="w-12 h-1 bg-gradient-to-r from-primary-500 to-blue-500 rounded-full"></div>
                </div>
              </div>
            </dl>
          </div>
        </div>

        <!-- 应用场景 -->
        <div class="mt-32">
          <div class="text-center mb-16">
            <h2 class="text-3xl font-bold tracking-tight text-primary-600 sm:text-4xl">应用场景</h2>
            <p class="mt-4 text-lg text-gray-600">
              广泛适用于各类教育咨询机构，提升服务效率和质量
            </p>
          </div>

          <div class="grid grid-cols-1 gap-8 lg:grid-cols-2">
            <div v-for="(scenario, index) in scenarios" :key="scenario.title" 
                 class="card p-8 opacity-0 fade-in-up"
                 :class="`delay-${(index + 1) * 200}`">
              <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ scenario.title }}</h3>
              <p class="text-gray-600 leading-relaxed">{{ scenario.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  AcademicCapIcon,
  ChartBarIcon,
  ClipboardDocumentCheckIcon,
  BuildingLibraryIcon,
  GlobeAltIcon,
  AcademicCapIcon as AcademicCapIcon2
} from '@heroicons/vue/24/outline'
import PageHeader from '@/components/PageHeader.vue'

const features = [
  {
    name: '客制化学习方案',
    description: '基于大模型的智能学习系统，通过评估用户背景，针对性制定学习方案，调用生成学习资料。',
    icon: AcademicCapIcon,
  },
  {
    name: '智能文案生成',
    description: '便捷使用，仅需少量提示词，即可深度调用数据库，为用户生成文案材料。',
    icon: ClipboardDocumentCheckIcon,
  },
  {
    name: '实况数据检测',
    description: '全方位监控主流留学目的地的政策变化、院校动态等实时信息，确保咨询内容的时效性。',
    icon: ChartBarIcon,
  },
  {
    name: '多模态信息处理',
    description: '支持文本、图像、音频等多种信息格式，提供全面的智能化服务体验。',
    icon: BuildingLibraryIcon,
  },
  {
    name: '本地化部署',
    description: '支持私有化部署，确保数据安全，满足机构对信息保密的严格要求。',
    icon: GlobeAltIcon,
  },
  {
    name: '持续学习优化',
    description: '系统具备自主学习能力，根据使用反馈不断优化服务质量和准确性。',
    icon: AcademicCapIcon2,
  }
]

const scenarios = [
  {
    title: '留学咨询机构',
    description: '为学生提供个性化的留学规划、院校推荐、申请材料准备等全方位服务，大幅提升咨询效率和成功率。'
  },
  {
    title: '语言培训机构',
    description: '根据学员水平和目标，智能生成个性化学习计划和练习材料，提供精准的语言学习指导。'
  },
  {
    title: '国际教育集团',
    description: '统一管理多个分支机构的教育资源，标准化服务流程，确保服务质量的一致性。'
  },
  {
    title: '在线教育平台',
    description: '集成AI智能助手，为用户提供24/7的学习支持和问题解答，提升用户体验和平台粘性。'
  }
]
</script>
