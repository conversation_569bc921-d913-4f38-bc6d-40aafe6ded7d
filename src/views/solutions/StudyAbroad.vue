<!-- 留学咨询解决方案页面 -->
<template>
  <div class="min-h-screen bg-gray-50">
    <PageHeader>
      留学咨询解决方案
      <template #subtitle>
        为留学咨询机构提供一站式AI支持，提升服务效率与转化率
      </template>
    </PageHeader>

    <div class="mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8">
      <!-- 方案概述 -->
      <div class="mx-auto max-w-4xl text-center opacity-0 fade-in-up">
        <h2 class="text-4xl lg:text-5xl font-bold tracking-tight text-gray-900">
          方案
          <span class="text-gradient bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent">概述</span>
        </h2>
        <p class="mt-8 text-xl leading-relaxed text-gray-600">
          解决方案整合了先进的大模型和多模态技术，在各环节为留学咨询行业服务增效，方案包含TunShuEdu AI智教平台的部署，可提供更多客制化功能选择。
        </p>
        
        <!-- 装饰性分隔线 -->
        <div class="mt-12 flex justify-center">
          <div class="w-24 h-1 bg-gradient-to-r from-primary-500 to-blue-500 rounded-full"></div>
        </div>
      </div>

      <!-- 核心功能 -->
      <div class="mx-auto mt-24 max-w-2xl sm:mt-32 lg:max-w-none">
        <div class="text-center mb-16 opacity-0 fade-in-up delay-200">
          <h3 class="text-3xl lg:text-4xl font-bold tracking-tight text-gray-900">
            核心
            <span class="text-gradient bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent">功能</span>
          </h3>
          <p class="mt-4 text-lg text-gray-600">全方位AI技术支持，提升咨询服务质量</p>
        </div>
        
        <dl class="grid max-w-xl grid-cols-1 gap-8 lg:max-w-none lg:grid-cols-3">
          <div v-for="(feature, index) in features" :key="feature.name" 
               class="card group hover-float p-8 text-center opacity-0 fade-in-up"
               :class="`delay-${300 + index * 100}`">
            <dt class="flex flex-col items-center">
              <div class="relative mb-6">
                <div class="absolute inset-0 bg-primary-500/20 rounded-full blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                <div class="relative flex h-16 w-16 items-center justify-center rounded-xl bg-gradient-to-br from-primary-500 to-blue-600 group-hover:from-primary-600 group-hover:to-blue-700 transition-all duration-300 shadow-lg">
                  <component :is="feature.icon" class="h-8 w-8 text-white group-hover:scale-110 transition-transform duration-300" aria-hidden="true" />
                </div>
              </div>
              <h4 class="text-xl font-semibold text-gray-900 group-hover:text-primary-600 transition-colors duration-300">
                {{ feature.name }}
              </h4>
            </dt>
            <dd class="mt-4 text-gray-600 leading-relaxed">
              {{ feature.description }}
            </dd>
            
            <!-- 悬浮时显示的装饰 -->
            <div class="mt-6 flex justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <div class="w-12 h-1 bg-gradient-to-r from-primary-500 to-blue-500 rounded-full"></div>
            </div>
          </div>
        </dl>
      </div>

      <!-- 服务流程 -->
      <div class="mx-auto mt-24 max-w-4xl sm:mt-32">
        <div class="text-center mb-16 opacity-0 fade-in-up delay-600">
          <h3 class="text-3xl lg:text-4xl font-bold tracking-tight text-gray-900">
            服务
            <span class="text-gradient bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent">流程</span>
          </h3>
          <p class="mt-4 text-lg text-gray-600">智能化流程管理，确保服务质量</p>
        </div>
        
        <div class="relative">
          <!-- 时间线背景 -->
          <div class="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-primary-500 via-blue-500 to-purple-500"></div>
          
          <div class="space-y-12">
            <div v-for="(step, stepIdx) in steps" :key="step.name"
                 class="relative opacity-0 fade-in-up"
                 :class="`delay-${700 + stepIdx * 100}`">
              <!-- 时间节点 -->
              <div class="absolute left-6 w-4 h-4 bg-white border-4 border-primary-500 rounded-full shadow-lg"></div>
              
              <!-- 内容卡片 -->
              <div class="ml-20">
                <div class="card group hover-float p-6">
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <div class="flex items-center space-x-3 mb-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-blue-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                          <component :is="step.icon" class="h-5 w-5 text-white" aria-hidden="true" />
                        </div>
                        <h4 class="text-lg font-semibold text-gray-900 group-hover:text-primary-600 transition-colors duration-300">
                          {{ step.name }}
                        </h4>
                      </div>
                      <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
                        {{ step.description }}
                      </p>
                    </div>
                  </div>
                  
                  <!-- 悬浮时显示的装饰 -->
                  <div class="mt-4 flex justify-start opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div class="w-16 h-1 bg-gradient-to-r from-primary-500 to-blue-500 rounded-full"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 方案优势 -->
      <div class="mx-auto mt-24 max-w-2xl sm:mt-32 lg:max-w-none">
        <div class="text-center mb-16 opacity-0 fade-in-up delay-1000">
          <h3 class="text-3xl lg:text-4xl font-bold tracking-tight text-gray-900">
            方案
            <span class="text-gradient bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent">优势</span>
          </h3>
          <p class="mt-4 text-lg text-gray-600">AI技术赋能，全面提升服务水平</p>
        </div>
        
        <div class="grid grid-cols-1 gap-8 lg:grid-cols-2">
          <div v-for="(advantage, index) in advantages" :key="advantage.title" 
               class="card group hover-float p-8 opacity-0 fade-in-up"
               :class="`delay-${1100 + index * 200}`">
            <div class="flex items-center mb-6">
              <div class="relative">
                <div class="absolute inset-0 bg-primary-500/20 rounded-full blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                <div class="relative flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-primary-500 to-blue-600 group-hover:from-primary-600 group-hover:to-blue-700 transition-all duration-300 shadow-lg">
                  <component :is="advantage.icon" class="h-6 w-6 text-white" aria-hidden="true" />
                </div>
              </div>
              <h4 class="ml-4 text-xl font-semibold text-gray-900 group-hover:text-primary-600 transition-colors duration-300">{{ advantage.title }}</h4>
            </div>
            <p class="text-gray-600 leading-relaxed mb-6">{{ advantage.description }}</p>
            <ul class="space-y-3">
              <li v-for="point in advantage.points" :key="point" class="flex gap-x-3">
                <div class="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                </div>
                <span class="text-sm text-gray-600">{{ point }}</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  UserGroupIcon,
  DocumentTextIcon,
  ChartBarIcon,
  RocketLaunchIcon,
  ChatBubbleLeftRightIcon,
  DocumentCheckIcon,
  ClockIcon,
  ShieldCheckIcon,
} from '@heroicons/vue/24/outline'
import PageHeader from '@/components/PageHeader.vue'

const features = [
  {
    name: '客户管理',
    description: '自动记录整理客户信息资料，监管申请流程进度，日历式讯息提醒及任务分配。',
    icon: UserGroupIcon,
  },
  {
    name: '文案生成助手',
    description: '基于大模型的文书写作助手，协助顾问高效完成文书、简历、推荐信等文案撰写。',
    icon: DocumentTextIcon,
  },
  {
    name: '选校定专推荐',
    description: '根据学生背景信息，深度调用案例及专业信息数据库，匹配冲稳保各级目标专业。',
    icon: ChartBarIcon,
  },
]

const steps = [
  {
    name: '需求咨询',
    description: 'AI助手协助顾问快速了解客户需求，智能推荐适合的留学方案。',
    icon: ChatBubbleLeftRightIcon,
  },
  {
    name: '方案制定',
    description: '基于案例及院校数据，为客户定制个性化的留学规划方案。',
    icon: DocumentTextIcon,
  },
  {
    name: '文书准备',
    description: 'AI文书助手协助高效完成各类申请文书的撰写和修改。',
    icon: DocumentCheckIcon,
  },
  {
    name: '申请递交',
    description: '智能化流程管理，确保申请材料完整性和时效性。',
    icon: RocketLaunchIcon,
  }
]

const advantages = [
  {
    title: '效率提升',
    description: '通过AI技术提高咨询服务效率，降低运营成本。',
    icon: ClockIcon,
    points: [
      '智能客户管理系统提高响应速度',
      'AI文书助手加速文书处理效率',
      '自动化流程减少人工操作时间',
      '数据分析辅助快速决策'
    ]
  },
  {
    title: '服务升级',
    description: '提供更专业、更个性化的留学咨询服务。',
    icon: ShieldCheckIcon,
    points: [
      '基于历史案例数据精准方案推荐',
      '个性化的留学规划建议',
      '全流程的智能化服务支持',
      '持续的服务质量监控和优化'
    ]
  }
]
</script>
