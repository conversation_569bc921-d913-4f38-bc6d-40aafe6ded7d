{"name": "tunshu-web", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview"}, "dependencies": {"@heroicons/vue": "^2.2.0", "vue": "^3.4.15", "vue-router": "^4.2.5"}, "devDependencies": {"@types/node": "^20.11.16", "@vitejs/plugin-vue": "^5.0.3", "autoprefixer": "^10.4.17", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.8.3", "vite": "^5.0.12", "vue-tsc": "^2.2.8"}}